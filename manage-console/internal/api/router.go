package api

import (
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler"
	"git.panlonggame.com/bkxplatform/manage-console/internal/middleware"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"github.com/gin-gonic/gin"
)

const (
	addr     = ":10001"
	BasePath = "/manage-console"
)

func StartServerGin() {
	logger.Logger.Infof("server port %s", addr)
	router := generateRouter()
	if err := router.Run(addr); err != nil {
		logger.Logger.Errorf("StartServerGin err: %v", err)
		panic(err)
	}
}

func generateRouter() *gin.Engine {
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.Use(middleware.Cors())
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.RequestID())
	router.Use(middleware.CleanPriorityHeader())

	group := router.Group(BasePath)
	group.GET("/ping", handler.SingletonUserHandler().Heartbeat)
	group.GET("/timestamp", handler.SingletonUserHandler().GetTimestamp)

	group.POST("/login", handler.SingletonUserHandler().Login)
	group.Use(middleware.JWT())

	group.POST("/users/get", handler.SingletonUserHandler().GetUsers)
	// 获取自身的账号权限
	group.POST("/permission/get", handler.SingletonPermissionHandler().GetPermission) // 1 获取自身账号权限 2 获取制定用户权限
	group.POST("/users/add", handler.SingletonUserHandler().AddUser)
	// 用户查询接口
	group.POST("/users/query", handler.SingletonUserQueryHandler().QueryUsers)
	// 新版本增加用户删除和修改
	group.POST("/users/del", handler.SingletonUserHandler().DeleteUser)
	group.POST("/users/put", handler.SingletonUserHandler().UpdateUser)
	// 新版本增加角色编辑
	group.POST("/roles/get", handler.SingletonPermissionHandler().GetRoles)
	group.POST("/roles/add", handler.SingletonPermissionHandler().AddRole)
	group.POST("/roles/put", handler.SingletonPermissionHandler().UpdateRole)
	group.POST("/roles/del", handler.SingletonPermissionHandler().DeleteRole)
	group.POST("/permission/config/get", handler.SingletonPermissionHandler().GetPermissionConfig)
	// 提交用户的数据权限（目前仅游戏）
	group.POST("/permission/user/save", handler.SingletonPermissionHandler().SaveUserPermission)
	// 新版本获取角色
	group.POST("/roles/dropdown", handler.SingletonPermissionHandler().GetRolesDropdown)

	// 获取游戏列表 （所有）/games/dropdown 根据系统是否决定获取所有
	group.POST("/games/get", handler.SingletonGameHandler().GetGames)
	group.POST("/games/dropdown", handler.SingletonGameHandler().GetGamesDropdown)
	group.POST("/game/detail", handler.SingletonGameHandler().GetGameDetail)
	group.POST("/game/add", handler.SingletonGameHandler().AddGame)
	group.POST("/game/put", handler.SingletonGameHandler().UpdateGame)
	group.POST("/game/del", handler.SingletonGameHandler().DeleteGame)

	// 游戏平台
	group.POST("/game/platform/get", handler.SingletonGamePlatformHandler().GetGamePlatforms)
	// group.POST("/game/platform/add", handler.SingletonGamePlatformHandler().AddGamePlatform)
	group.POST("/game/platform/put", handler.SingletonGamePlatformHandler().UpdateGamePlatform)
	// group.POST("/game/platform/del", handler.SingletonGamePlatformHandler().DeleteGamePlatform)
	group.POST("/game/platform/secret/put", handler.SingletonGamePlatformHandler().UpdateGamePlatformSecret)

	// 支付参数配置
	group.POST("/game/payment/config/get", handler.SingletonPaymentConfigHandler().GetPaymentConfig)
	group.POST("/game/payment/config/put", handler.SingletonPaymentConfigHandler().UpdatePaymentConfig)

	// 针对于游戏的引力开关控制
	group.POST("/game/gravity/add", handler.SingletonGameHandler().AddGameGravitySwitch)
	group.POST("/game/gravity/put", handler.SingletonGameHandler().UpdateGameGravitySwitch)

	// 分享
	group.POST("/share/get", handler.SingletonShareHandler().GetShares)
	group.POST("/share/add", handler.SingletonShareHandler().AddShare)
	group.POST("/share/put", handler.SingletonShareHandler().UpdateShare)
	group.POST("/share/del", handler.SingletonShareHandler().DeleteShare)
	// 分享Roadblock
	group.POST("/share/roadblock/get", handler.SingletonShareHandler().GetRoadblocks)
	group.POST("/share/roadblock/add", handler.SingletonShareHandler().AddRoadblock)
	group.POST("/share/roadblock/put", handler.SingletonShareHandler().UpdateRoadblock)
	group.POST("/share/roadblock/del", handler.SingletonShareHandler().DeleteRoadblock)

	// 兑换码
	group.POST("/redemption/code/get", handler.SingletonRedemptionHandler().GetRedemptionCode)
	group.POST("/redemption/code/add", handler.SingletonRedemptionHandler().AddRedemptionCode)
	group.POST("/redemption/code/put", handler.SingletonRedemptionHandler().UpdateRedemptionCode)
	group.POST("/redemption/code/del", handler.SingletonRedemptionHandler().DeleteRedemptionCode)
	group.POST("/redemption/code/export", handler.SingletonRedemptionHandler().ExportRedemptionCode)
	group.POST("/redemption/entity/get", handler.SingletonRedemptionHandler().GetRedemptionEntity)

	// 敏感词管理
	group.POST("/sensitive/word/get", handler.SingletonSensitiveHandler().GetSensitiveWords)
	group.POST("/sensitive/word/add", handler.SingletonSensitiveHandler().AddSensitiveWord)
	group.POST("/sensitive/word/put", handler.SingletonSensitiveHandler().UpdateSensitiveWord)
	group.POST("/sensitive/word/del", handler.SingletonSensitiveHandler().DeleteSensitiveWord)
	group.POST("/sensitive/word/import", handler.SingletonSensitiveHandler().ImportSensitiveWord)
	group.POST("/sensitive/word/export", handler.SingletonSensitiveHandler().ExportSensitiveWord)
	group.POST("/sensitive/word/config/get", handler.SingletonSensitiveHandler().GetConfigSensitiveWord)
	group.POST("/sensitive/word/config/add", handler.SingletonSensitiveHandler().AddConfigSensitiveWord)

	// 商品管理
	group.POST("/goods/get", handler.SingletonGoodsHandler().GetGoods)
	group.POST("/goods/add", handler.SingletonGoodsHandler().AddGoods)
	group.POST("/goods/put", handler.SingletonGoodsHandler().UpdateGoods)
	group.POST("/goods/del", handler.SingletonGoodsHandler().DeleteGoods)

	// 批量导入商品管理
	group.POST("/goods/import", handler.SingletonGoodsHandler().ImportGoods)

	// 客服消息
	group.POST("/customer_service/msg/add", handler.SingletonCustomerHandler().AddCustomerServiceMsg)
	group.POST("/customer_service/msg/get", handler.SingletonCustomerHandler().GetCustomerServiceMsg)
	group.POST("/customer_service/msg/put", handler.SingletonCustomerHandler().UpdateCustomerServiceMsg)
	group.POST("/customer_service/msg/del", handler.SingletonCustomerHandler().DeleteCustomerServiceMsg)

	// 广告位管理
	group.POST("/ad/position/get", handler.SingletonAdHandler().GetAdPositions)
	group.POST("/ad/position/upsert", handler.SingletonAdHandler().UpsertAdPosition)
	// group.POST("/ad/position/add", handler.SingletonAdHandler().AddAdPosition)
	// group.POST("/ad/position/put", handler.SingletonAdHandler().UpdateAdPosition)
	group.POST("/ad/position/del", handler.SingletonAdHandler().DeleteAdPosition)

	// 订单列表
	group.POST("/orders/get", handler.SingletonOrderHandler().GetOrders)
	group.POST("/orders/download", handler.SingletonOrderHandler().DownloadOrders)
	// 如遇到发货失败的，一键补单
	group.POST("/orders/reissue", handler.SingletonOrderHandler().ReissueOrder)

	// 文件上传
	group.POST("/upload", handler.SingletonFileHandler().UploadFile)

	// 自定义开关
	group.POST("/switch/get", handler.SingletonSwitchHandler().GetSwitch)
	group.POST("/switch/add", handler.SingletonSwitchHandler().AddSwitch)
	group.POST("/switch/put", handler.SingletonSwitchHandler().UpdateSwitch)
	group.POST("/switch/del", handler.SingletonSwitchHandler().DeleteSwitch)
	group.POST("/switch/test/get", handler.SingletonSwitchHandler().GetTestSwitch)
	group.POST("/switch/scene/value/list", handler.SingletonSwitchHandler().GetSwitchSceneValueList)
	group.POST("/switch/city/code/list", handler.SingletonSwitchHandler().GetSwitchCityCodeList)

	// 举报系统
	group.POST("/report/get", handler.SingletonReportHandler().GetReport)
	// 举报系统-详情
	group.POST("/report/detail/get", handler.SingletonReportHandler().GetReportDetail)
	group.POST("/report/put", handler.SingletonReportHandler().UpdateReport)
	group.POST("/report/del", handler.SingletonReportHandler().DeleteReport)
	group.POST("/report/extra/download", handler.SingletonReportHandler().DownloadReportExtra)
	// 举报配置管理API
	group.POST("/report/config/item/get", handler.SingletonReportConfigHandler().ListReportItemConfig)
	group.POST("/report/config/item/upsert", handler.SingletonReportConfigHandler().UpsertReportItemConfig)
	group.POST("/report/config/item/del", handler.SingletonReportConfigHandler().DeleteReportItemConfig)

	group.POST("/report/config/action/get", handler.SingletonReportConfigHandler().ListReportActionConfig)
	group.POST("/report/config/action/upsert", handler.SingletonReportConfigHandler().UpsertReportActionConfig)
	group.POST("/report/config/action/del", handler.SingletonReportConfigHandler().DeleteReportActionConfig)
	group.POST("/report/config/action/dropdown", handler.SingletonReportConfigHandler().ListReportActionDropdown)

	// 游戏内容监控系统
	group.POST("/monitor/list", handler.SingletonContentHandler().GetContentList)
	group.POST("/monitor/download", handler.SingletonContentHandler().DownloadContentList)
	group.POST("/monitor/config/get", handler.SingletonContentHandler().GetSourceMapping)
	group.POST("/monitor/config/upsert", handler.SingletonContentHandler().UpdateSourceMapping)
	group.POST("/monitor/config/del", handler.SingletonContentHandler().DeleteSourceMapping)
	// 监控内容处理记录
	group.POST("/monitor/processing", handler.SingletonContentHandler().CreateProcessing)
	group.POST("/monitor/processing/list", handler.SingletonContentHandler().GetProcessingList)
	// 监控内容-根据user_id查找用户付款总金额，a_order表中的money,之后找到a_user的created_at返回这个用户的注册时间
	group.POST("/monitor/user/payment", handler.SingletonContentHandler().GetUserPaymentInfo)

	// 验证码配置
	group.POST("/config/captcha/get", handler.SingletonCaptchaHandler().GetCaptchaConfig)         // 获取验证码配置接口
	group.POST("/config/captcha/operate", handler.SingletonCaptchaHandler().OperateCaptchaConfig) // 新增验证码配置接口

	group.POST("/user/ban/add", handler.SingletonUserHandler().AddUserBan)

	// H5打包
	group.POST("/pkg/task/add", handler.SingletonPkgHandler().AddPkgTask)
	group.POST("/pkg/task/get", handler.SingletonPkgHandler().GetPkgTasks)
	group.POST("/pkg/task/import", handler.SingletonH5AdminHandler().ImportH5AdminUsers)
	// group.POST("/pkg/task/detail", handler.SingletonPkgHandler().GetPkgTaskDetail)

	// 财务对账-收入统计
	group.POST("/finance/income/get", handler.SingletonFinanceHandler().GetIncome)
	group.POST("/finance/income/download", handler.SingletonFinanceHandler().DownloadIncome)
	// 工单系统
	group.POST("/workorder/get", handler.SingletonWorkOrderHandler().GetWorkOrders)                      // 工单列表
	group.POST("/workorder/statistics", handler.SingletonWorkOrderHandler().GetWorkOrderStatistics)      // 工单统计
	group.POST("/workorder/detail/get", handler.SingletonWorkOrderHandler().GetWorkOrderDetail)          // 工单详情
	group.POST("/workorder/accept", handler.SingletonWorkOrderHandler().AcceptWorkOrder)                 // 接单
	group.POST("/workorder/complete", handler.SingletonWorkOrderHandler().CompleteWorkOrder)             // 完结工单
	group.POST("/workorder/reopen", handler.SingletonWorkOrderHandler().ReopenWorkOrder)                 // 重新开单
	group.POST("/workorder/reply", handler.SingletonWorkOrderHandler().ReplyWorkOrder)                   // 回复工单
	group.POST("/workorder/batch/accept", handler.SingletonWorkOrderHandler().BatchAcceptWorkOrders)     // 批量接单
	group.POST("/workorder/batch/complete", handler.SingletonWorkOrderHandler().BatchCompleteWorkOrders) // 批量完结
	group.POST("/workorder/priority/put", handler.SingletonWorkOrderHandler().UpdateWorkOrderPriority)   // 更新优先级
	group.POST("/workorder/remark/put", handler.SingletonWorkOrderHandler().UpdateWorkOrderRemark)       // 更新备注
	group.POST("/workorder/reply/put", handler.SingletonWorkOrderHandler().UpdateWorkOrderReply)         // 修改工单回复的内容
	// 工单标签管理
	group.POST("/workorder/tags/get", handler.SingletonWorkOrderHandler().GetWorkOrderTags)    // 获取所有标签
	group.POST("/workorder/tags/add", handler.SingletonWorkOrderHandler().AddWorkOrderTag)     // 添加标签
	group.POST("/workorder/tags/put", handler.SingletonWorkOrderHandler().UpdateWorkOrderTags) // 更新工单标签

	// 获取受理人列表
	group.POST("/workorder/acceptors/get", handler.SingletonWorkOrderHandler().GetWorkOrderAcceptors)

	// import origin workorder xlsx
	// group.POST("/workorder/import/xlsx", handler.SingletonWorkOrderHandler().ImportOriginWorkOrderXlsx)

	// 工单游戏展示配置
	group.POST("/workorder/config/get", handler.SingletonWorkOrderHandler().GetWorkOrderConfigs)
	group.POST("/workorder/config/upsert", handler.SingletonWorkOrderHandler().UpsertWorkOrderConfig)
	group.POST("/workorder/config/del", handler.SingletonWorkOrderHandler().DeleteWorkOrderConfig)
	// group.POST("/workorder/config/add", handler.SingletonWorkOrderHandler().AddWorkOrderConfig)
	// group.POST("/workorder/config/put", handler.SingletonWorkOrderHandler().UpdateWorkOrderConfig)
	// 工单游戏审核开关和版本号
	group.POST("/workorder/review_switch/get", handler.SingletonWorkOrderHandler().GetWorkOrderReviewSwitch)
	group.POST("/workorder/review_switch/upsert", handler.SingletonWorkOrderHandler().UpsertWorkOrderReviewSwitch)
	// 工单繁忙提示开关
	group.POST("/workorder/busy_switch/get", handler.SingletonWorkOrderHandler().GetWorkOrderBusySwitch)
	group.POST("/workorder/busy_switch/upsert", handler.SingletonWorkOrderHandler().UpdateWorkOrderBusySwitch)

	// 工单关注功能
	group.POST("/workorder/follow", handler.SingletonWorkOrderHandler().FollowWorkOrder)     // 关注工单
	group.POST("/workorder/unfollow", handler.SingletonWorkOrderHandler().UnfollowWorkOrder) // 取消关注

	// 问题库管理
	group.POST("/question/library/get", handler.SingletonQuestionHandler().GetQuestionLibrary)
	group.POST("/question/library/upsert", handler.SingletonQuestionHandler().UpsertQuestionLibrary)
	group.POST("/question/library/del", handler.SingletonQuestionHandler().DeleteQuestionLibrary)
	group.POST("/question/library/dropdown", handler.SingletonQuestionHandler().GetQuestionLibraryDropdown)
	// 问题库编辑系统提示词
	group.POST("/question/system_prompt/get", handler.SingletonQuestionHandler().GetQuestionSystemPrompt)
	group.POST("/question/system_prompt/upsert", handler.SingletonQuestionHandler().UpsertQuestionSystemPrompt)

	// 问题欢迎语管理
	group.POST("/question/welcome/get", handler.SingletonQuestionHandler().GetWelcomeMessage)
	group.POST("/question/welcome/upsert", handler.SingletonQuestionHandler().UpsertWelcomeMessage)
	group.POST("/question/welcome/del", handler.SingletonQuestionHandler().DeleteWelcomeMessage)

	// 停服配置管理
	group.POST("/stop_service_config/get", handler.SingletonStopServiceConfigHandler().GetStopServiceConfig)
	group.POST("/stop_service_config/upsert", handler.SingletonStopServiceConfigHandler().UpsertStopServiceConfig)

	// 战力排行榜查询
	group.POST("/third_party/power_ranking", handler.SingletonPowerRankingHandler().QueryPowerRanking)
	// 获取区服列表
	group.POST("/third_party/server_list", handler.SingletonPowerRankingHandler().GetServerList)

	// VIP用户管理
	group.POST("/vip/user/list", handler.SingletonVIPHandler().GetVIPUsers)                             // 获取VIP用户列表
	group.POST("/vip/user/authenticate", handler.SingletonVIPHandler().AuthenticateVIPUser)             // 认证VIP用户
	group.POST("/vip/user/batch_authenticate", handler.SingletonVIPHandler().BatchAuthenticateVIPUsers) // 批量认证VIP用户
	// VIP分配规则管理
	group.POST("/vip/rule/get", handler.SingletonVIPHandler().GetVIPRule)       // 获取分配规则
	group.POST("/vip/rule/update", handler.SingletonVIPHandler().UpdateVIPRule) // 更新分配规则
	// VIP统计概览
	group.POST("/vip/stats/overview", handler.SingletonVIPHandler().GetVIPStats) // 获取VIP用户统计概览
	// 客服管理
	group.POST("/vip/customer_service/get", handler.SingletonVIPHandler().GetCustomerService)       // 获取客服信息
	group.POST("/vip/customer_service/update", handler.SingletonVIPHandler().UpdateCustomerService) // 更新客服信息
	// 游戏端接口（不需要JWT认证）
	router.POST(BasePath+"/api/vip/popup/check", handler.SingletonVIPHandler().CheckVIPPopup) // 检查VIP弹窗
	router.POST(BasePath+"/api/vip/status/get", handler.SingletonVIPHandler().GetVIPStatus)   // 获取VIP状态

	return router
}
