package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"strconv"
	"sync"

	"git.panlonggame.com/bkxplatform/manage-console/internal/constants"
	"git.panlonggame.com/bkxplatform/manage-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/manage-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store"
	"git.panlonggame.com/bkxplatform/manage-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/manage-console/pkg/redis"
	"github.com/jinzhu/copier"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

var (
	_sensitiveOnce    sync.Once
	_sensitiveService *SensitiveService
)

type SensitiveService struct{}

func SingletonSensitiveService() *SensitiveService {
	_sensitiveOnce.Do(func() {
		_sensitiveService = &SensitiveService{}
	})
	return _sensitiveService
}

// GetSensitiveWordByID 根据ID获取敏感词
func (s *SensitiveService) GetSensitiveWordByID(ctx context.Context, id int32) (*model.MSensitiveWord, error) {
	word := store.QueryDB().MSensitiveWord
	return word.WithContext(ctx).Where(word.ID.Eq(id)).Where(word.IsDeleted.Zero()).First()
}

func (s *SensitiveService) GetSensitivesWordByID(ctx context.Context, id []int32) ([]*model.MSensitiveWord, error) {
	word := store.QueryDB().MSensitiveWord
	return word.WithContext(ctx).Where(word.ID.In(id...)).Where(word.IsDeleted.Zero()).Find()
}

func (s *SensitiveService) GetSensitiveWords(ctx context.Context, req *bean.GetSensitiveWordsReq) (*bean.GetSensitiveWordsRes, error) {
	word := store.QueryDB().MSensitiveWord
	wordCtx := word.WithContext(ctx)
	if len(req.Level) != 0 {
		wordCtx = wordCtx.Where(word.Level.In(req.Level...))
	}
	if req.Content != "" {
		wordCtx = wordCtx.Where(word.Content.Like("%" + req.Content + "%"))
	}

	words, total, err := wordCtx.
		Where(word.GameID.Eq(req.GameID)).
		Where(word.IsDeleted.Zero()).
		Order(word.CreatedAt.Desc()).
		FindByPage((req.Page-1)*req.Limit, req.Limit)
	if err != nil {
		return nil, err
	}

	results := make([]*bean.SensitiveWord, 0, len(words))
	err = copier.Copy(&results, words)
	if err != nil {
		return nil, err
	}
	return &bean.GetSensitiveWordsRes{
		SensitiveWords: results,
		Total:          total,
	}, nil
}

func (s *SensitiveService) AddSensitiveWord(ctx context.Context, req *bean.AddSensitiveWordReq) error {
	if err := store.QueryDB().MSensitiveWord.WithContext(ctx).Create(&model.MSensitiveWord{
		GameID:  req.GameID,
		Content: req.Content,
		Level:   req.Level,
	}); err != nil {
		return err
	}
	return nil
}

// HAddSensitiveWord 保存敏感词到redis
func (s *SensitiveService) HAddSensitiveWord(ctx context.Context, gameID string, content string, level int32) error {
	if err := redis.SAdd(ctx, fmt.Sprintf(constants.RedisSensitiveHSetInfo, gameID), fmt.Sprintf("%d:%s", level, content)); err != nil {
		return err
	}
	return nil
}

// DelSensitiveWord 从redis删除敏感词（删除整个gameID的SET，保留用于兼容性）
func (s *SensitiveService) DelSensitiveWord(ctx context.Context, gameID string) error {
	if err := redis.Del(ctx, fmt.Sprintf(constants.RedisSensitiveHSetInfo, gameID)); err != nil {
		return err
	}
	return nil
}

// DelSpecificSensitiveWord 从redis删除特定的敏感词
func (s *SensitiveService) DelSpecificSensitiveWord(ctx context.Context, gameID string, content string, level int32) error {
	member := fmt.Sprintf("%d:%s", level, content)
	if err := redis.SRem(ctx, fmt.Sprintf(constants.RedisSensitiveHSetInfo, gameID), member); err != nil {
		return err
	}
	return nil
}

// PublishSensitiveWord 发布关键词信号
func (s *SensitiveService) PublishSensitiveWord(ctx context.Context, key string, gameID string, level int32, content string) error {
	publish, err := redis.Publish(ctx, key, fmt.Sprintf("%s:%d:%s", gameID, level, content))
	if err != nil {
		return err
	}
	logger.Logger.Debugf("PublishSensitiveWord: redis publish %d", publish)
	return nil
}

// PublishSensitiveWordConfig 发布关键词配置信号
func (s *SensitiveService) PublishSensitiveWordConfig(ctx context.Context, key string, gameID string, ignoreCase int32) error {
	publish, err := redis.Publish(ctx, key, fmt.Sprintf("%s:%d", gameID, ignoreCase))
	if err != nil {
		return err
	}
	logger.Logger.Debugf("PublishSensitiveWord: redis publish %d", publish)
	return nil
}

func (s *SensitiveService) UpdateSensitiveWord(ctx context.Context, req *bean.UpdateSensitiveWordReq) error {
	word := store.QueryDB().MSensitiveWord
	if _, err := word.WithContext(ctx).Where(word.ID.Eq(req.ID)).Updates(&model.MSensitiveWord{
		Content: req.Content,
		Level:   req.Level,
	}); err != nil {
		return err
	}
	return nil
}

func (s *SensitiveService) DeleteSensitiveWord(ctx context.Context, req *bean.DeleteSensitiveWordReq) error {
	word := store.QueryDB().MSensitiveWord
	if _, err := word.WithContext(ctx).Where(word.ID.In(req.ID...)).UpdateSimple(word.IsDeleted.Value(true)); err != nil {
		return err
	}
	return nil
}

func (s *SensitiveService) ImportSensitiveWord(ctx context.Context, gameID string, file multipart.File) error {
	// 读取文件内容到内存中
	data, err := io.ReadAll(file)
	if err != nil {
		return err
	}

	f, err := excelize.OpenReader(io.NopCloser(bytes.NewReader(data)))
	if err != nil {
		return err
	}

	// 获取Sheet1的数据
	rows, err := f.GetRows("Sheet1")
	if err != nil {
		logger.Logger.Errorf("ImportSensitiveWord err: %s Sheet1 not found", gameID)
		return err
	}

	word := store.QueryDB().MSensitiveWord
	words := make([]*model.MSensitiveWord, 0)
	for i, row := range rows {
		if i == 0 {
			continue
		}
		level, err := strconv.Atoi(row[1])
		if err != nil {
			return fmt.Errorf("ImportSensitiveWord :%s is not number", row[1])
		}
		if row[0] == "" || level <= 0 || level > 3 {
			logger.Logger.Errorf("ImportSensitiveWord err: 敏感词为空或等级不合法，敏感词:%s, 等级:%d, 导入终止", row[0], level)
			return constants.ErrSensitiveWordParam
		}
		words = append(words, &model.MSensitiveWord{
			GameID:  gameID,
			Content: row[0],
			Level:   int32(level),
		})
	}
	if err = word.WithContext(ctx).CreateInBatches(words, len(words)); err != nil {
		return err
	}

	// 同步到redis
	for _, w := range words {
		if err = s.HAddSensitiveWord(ctx, w.GameID, w.Content, w.Level); err != nil {
			return err
		}
	}

	return nil
}

// ExportSensitiveWord
func (s *SensitiveService) ExportSensitiveWord(ctx context.Context, req *bean.ExportSensitiveWordReq) (*bean.ExportSensitiveWordResp, error) {
	word := store.QueryDB().MSensitiveWord
	wordCtx := word.WithContext(ctx)
	words, err := wordCtx.Where(word.GameID.Eq(req.GameID)).Where(word.IsDeleted.Zero()).Order(word.CreatedAt.Desc()).Find()
	if err != nil {
		return nil, err
	}
	if len(words) == 0 {
		return nil, constants.ErrWordsNotFoundData
	}

	f := excelize.NewFile()
	defer f.Close()

	index, err := f.NewSheet(constants.DefaultSheetName)
	if err != nil {
		return nil, err
	}

	header := [2]string{"敏感词", "等级"}
	headerCol := [2]string{"A", "B"}
	for i, e := range header {
		err = f.SetCellValue(constants.DefaultSheetName, fmt.Sprintf("%s1", headerCol[i]), e)
		if err != nil {
			return nil, err
		}
	}

	for i, e := range words {
		row := i + 2
		err = f.SetCellValue(constants.DefaultSheetName, fmt.Sprintf("A%d", row), e.Content)
		if err != nil {
			return nil, err
		}
		err = f.SetCellValue(constants.DefaultSheetName, fmt.Sprintf("B%d", row), e.Level)
		if err != nil {
			return nil, err
		}
	}
	f.SetActiveSheet(index)

	var buf bytes.Buffer
	err = f.Write(&buf)
	if err != nil {
		return nil, err
	}

	return &bean.ExportSensitiveWordResp{
		FileName: util.UUIDWithoutHyphens(),
		Data:     buf.Bytes(),
	}, nil
}

// GetConfigSensitiveWord 获取敏感词设置
func (s *SensitiveService) GetConfigSensitiveWord(ctx context.Context, gameID string) (*bean.GetConfigSensitiveWordResp, error) {
	conf := store.QueryDB().MSensitiveWordConfig
	config, err := conf.WithContext(ctx).Where(conf.GameID.Eq(gameID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Infof("GetConfigSensitiveWord tip: 此游戏没有设置是否忽略大小写，默认不忽略大小写")
		return &bean.GetConfigSensitiveWordResp{
			GameID:     gameID,
			IgnoreCase: constants.IgnoreCaseClose,
		}, nil
	} else if err != nil {
		return nil, err
	}
	return &bean.GetConfigSensitiveWordResp{
		GameID:     config.GameID,
		IgnoreCase: config.IgnoreCase,
	}, nil
}

// AddConfigSensitiveWord 添加敏感词设置
func (s *SensitiveService) AddConfigSensitiveWord(ctx context.Context, req *bean.AddConfigSensitiveWordReq) (*bean.GetConfigSensitiveWordResp, error) {
	conf := store.QueryDB().MSensitiveWordConfig
	_, err := conf.WithContext(ctx).Where(conf.GameID.Eq(req.GameID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		if err = conf.WithContext(ctx).Create(&model.MSensitiveWordConfig{
			GameID:     req.GameID,
			IgnoreCase: req.IgnoreCase,
		}); err != nil {
			return nil, err
		}

		if err := s.addSensitiveWordConfig(ctx, req.GameID, req.IgnoreCase); err != nil {
			return nil, err
		}
		return s.GetConfigSensitiveWord(ctx, req.GameID)
	}
	if _, err := conf.WithContext(ctx).Where(conf.GameID.Eq(req.GameID)).UpdateSimple(conf.IgnoreCase.Value(req.IgnoreCase)); err != nil {
		return nil, err
	}

	// hash 结构直接覆盖，因此可直接add
	if err := s.addSensitiveWordConfig(ctx, req.GameID, req.IgnoreCase); err != nil {
		return nil, err
	}
	return s.GetConfigSensitiveWord(ctx, req.GameID)
}

func (s *SensitiveService) addSensitiveWordConfig(ctx context.Context, gameID string, ignoreCase int32) error {
	if err := redis.HSet(ctx, constants.RedisSensitiveConfigHSetInfo, gameID, ignoreCase); err != nil {
		return err
	}
	err := s.PublishSensitiveWordConfig(ctx, constants.RedisSensitiveConfigPublishAdd, gameID, ignoreCase)
	return err
}
